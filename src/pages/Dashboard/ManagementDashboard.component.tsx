import React from 'react'
import { motion } from 'framer-motion'
import MainLayout from '../../components/layout/MainLayout'
import LeaderboardCard from './sub-components/LeaderboardCard'
import OverviewMetricsCard from './sub-components/OverviewMetricsCard'
import UserAnalyticsCard from './sub-components/UserAnalyticsCard'
import TaskSetsAnalyticsCard from './sub-components/TaskSetsAnalyticsCard'

import TopDateFilter from './sub-components/TopDateFilter'
import useManagementDashboard from './ManagementDashboard.container'
import { useLeaderboard } from '../../hooks/useLearningStats'

/**
 * Management Dashboard Component - Modern admin dashboard with sleek animations
 * Features overview metrics, user analytics, task sets analytics, and date filtering
 */
const ManagementDashboard: React.FC = () => {
  const {
    // State
    overview,
    overviewLoading,
    overviewError,
    usersMetrics,
    usersLoading,
    usersError,
    taskSetsMetrics,
    taskSetsLoading,
    taskSetsError,
    startDate,
    endDate,
    user,
    
    // Actions
    handleDateChange,
    handleDateReset,
    refreshOverview,
    refreshUsersMetrics,
    refreshTaskSetsMetrics
  } = useManagementDashboard()

  // Get leaderboard data (keeping existing functionality)
  const {
    leaderboard,
    loading: leaderboardLoading,
    error: leaderboardError,
    refetch: refreshLeaderboard
  } = useLeaderboard({ skip: 0, limit: 10 })

  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: [0.25, 0.46, 0.45, 0.94] as const
      }
    }
  }



  const staggerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  }

  return (
    <MainLayout
      title="Management Dashboard"
      description={`Admin Overview - Welcome back, ${user?.full_name || user?.username}!`}
    >

      {/* Fixed Layout Container - No scrolling on main container */}
      <div className="h-screen flex flex-col">

        {/* Top Date Filter - Fixed at top */}
        <motion.div
          variants={cardVariants}
          className="flex-shrink-0 p-4 border-b border-border"
        >
          <TopDateFilter
            startDate={startDate}
            endDate={endDate}
            onDateChange={handleDateChange}
            onReset={handleDateReset}
            cardVariants={cardVariants}
          />
        </motion.div>

        {/* Main Content Grid - Fixed height, no overflow */}
        <div className="flex-1 grid grid-cols-1 lg:grid-cols-4 gap-0 min-h-0">

          {/* Left Side Content - Scrollable content area only */}
          <div className="lg:col-span-3 flex flex-col min-h-0">

            {/* Overview Metrics - Fixed height */}
            <motion.div
              variants={cardVariants}
              className="flex-shrink-0 p-4"
            >
              <OverviewMetricsCard
                data={overview}
                taskSetsData={taskSetsMetrics}
                loading={overviewLoading}
                error={overviewError}
                onRefresh={refreshOverview}
                cardVariants={cardVariants}
              />
            </motion.div>

            {/* Scrollable Charts Area */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">

              {/* User Analytics - Line Race Chart */}
              <motion.div
                variants={staggerVariants}
                className="h-[400px]"
              >
                <motion.div variants={cardVariants} className="h-full">
                  <UserAnalyticsCard
                    data={usersMetrics}
                    loading={usersLoading}
                    error={usersError}
                    onRefresh={refreshUsersMetrics}
                    cardVariants={cardVariants}
                  />
                </motion.div>
              </motion.div>

              {/* Task Sets Analytics - Nivo Sunburst Chart */}
              <motion.div
                variants={staggerVariants}
                className="h-[500px]"
              >
                <motion.div variants={cardVariants} className="h-full">
                  <TaskSetsAnalyticsCard
                    data={taskSetsMetrics}
                    loading={taskSetsLoading}
                    error={taskSetsError}
                    onRefresh={refreshTaskSetsMetrics}
                    cardVariants={cardVariants}
                  />
                </motion.div>
              </motion.div>

            </div>
          </div>

          {/* Right Side - Fixed Leaderboard (No scrolling) */}
          <motion.div
            className="lg:col-span-1 border-l border-border"
            variants={cardVariants}
          >
            <div className="h-full p-4">
              <LeaderboardCard
                leaderboard={leaderboard}
                loading={leaderboardLoading}
                error={leaderboardError}
                onRefresh={refreshLeaderboard}
                cardVariants={cardVariants}
              />
            </div>
          </motion.div>

        </div>
      </div>
    </MainLayout>
  )
}

export default ManagementDashboard
