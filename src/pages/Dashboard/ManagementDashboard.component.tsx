import React from 'react'
import { motion } from 'framer-motion'
import MainLayout from '../../components/layout/MainLayout'
import LeaderboardCard from './sub-components/LeaderboardCard'
import OverviewMetricsCard from './sub-components/OverviewMetricsCard'
import UserAnalyticsCard from './sub-components/UserAnalyticsCard'
import TaskSetsAnalyticsCard from './sub-components/TaskSetsAnalyticsCard'

import TopDateFilter from './sub-components/TopDateFilter'
import useManagementDashboard from './ManagementDashboard.container'
import { useLeaderboard } from '../../hooks/useLearningStats'

/**
 * Management Dashboard Component - Modern admin dashboard with sleek animations
 * Features overview metrics, user analytics, task sets analytics, and date filtering
 */
const ManagementDashboard: React.FC = () => {
  const {
    // State
    overview,
    overviewLoading,
    overviewError,
    usersMetrics,
    usersLoading,
    usersError,
    taskSetsMetrics,
    taskSetsLoading,
    taskSetsError,
    startDate,
    endDate,
    user,
    
    // Actions
    handleDateChange,
    handleDateReset,
    refreshOverview,
    refreshUsersMetrics,
    refreshTaskSetsMetrics
  } = useManagementDashboard()

  // Get leaderboard data (keeping existing functionality)
  const {
    leaderboard,
    loading: leaderboardLoading,
    error: leaderboardError,
    refetch: refreshLeaderboard
  } = useLeaderboard({ skip: 0, limit: 10 })

  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    }
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        duration: 0.3
      }
    }
  }

  const staggerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  }

  return (
    <MainLayout
      title="Management Dashboard"
      description={`Admin Overview - Welcome back, ${user?.full_name || user?.username}!`}
    >
      {/* Animated Background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/20 via-transparent to-purple-50/20 dark:from-blue-950/10 dark:via-transparent dark:to-purple-950/10" />
        <motion.div 
          className="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-br from-blue-200/5 to-purple-200/5 dark:from-blue-800/3 dark:to-purple-800/3 rounded-full blur-3xl"
          animate={{ 
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3]
          }}
          transition={{ 
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div 
          className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-br from-purple-200/5 to-pink-200/5 dark:from-purple-800/3 dark:to-pink-800/3 rounded-full blur-3xl"
          animate={{ 
            scale: [1.2, 1, 1.2],
            opacity: [0.5, 0.3, 0.5]
          }}
          transition={{ 
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="relative z-10 w-full mx-auto px-2 sm:px-4 lg:px-6 py-2"
      >
        {/* Main Grid Layout - Left content 75%, Right leaderboard 25% */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 min-h-[calc(100vh-6rem)]">

          {/* Left Side Content - Takes 75% (3 columns) */}
          <div className="lg:col-span-3 space-y-6 overflow-y-auto max-h-[calc(100vh-6rem)]">

            {/* Top Date Filter - Only for left side */}
            <TopDateFilter
              startDate={startDate}
              endDate={endDate}
              onDateChange={handleDateChange}
              onReset={handleDateReset}
              cardVariants={cardVariants}
            />

            {/* Overview Metrics - Full width */}
            <motion.div variants={cardVariants}>
              <OverviewMetricsCard
                data={overview}
                taskSetsData={taskSetsMetrics}
                loading={overviewLoading}
                error={overviewError}
                onRefresh={refreshOverview}
                cardVariants={cardVariants}
              />
            </motion.div>

            {/* Charts Grid - User Analytics takes full width */}
            <motion.div
              variants={staggerVariants}
              className="grid grid-cols-1 gap-6 h-[500px]"
            >
              {/* User Analytics - Line Race Chart */}
              <motion.div variants={cardVariants} className="h-full">
                <UserAnalyticsCard
                  data={usersMetrics}
                  loading={usersLoading}
                  error={usersError}
                  onRefresh={refreshUsersMetrics}
                  cardVariants={cardVariants}
                />
              </motion.div>
            </motion.div>

            {/* Task Sets Analytics - Nivo Sunburst Chart (Full Width) */}
            <motion.div
              variants={staggerVariants}
              className="grid grid-cols-1 gap-6 h-[600px] mt-8"
            >
              <motion.div variants={cardVariants} className="h-full">
                <TaskSetsAnalyticsCard
                  data={taskSetsMetrics}
                  loading={taskSetsLoading}
                  error={taskSetsError}
                  onRefresh={refreshTaskSetsMetrics}
                  cardVariants={cardVariants}
                />
              </motion.div>
            </motion.div>

          </div>

          {/* Right Side - Leaderboard takes 25% (1 column) - Fixed position */}
          <motion.div
            className="lg:col-span-1 h-[calc(100vh-6rem)]"
            variants={cardVariants}
          >
            <div className="h-full">
              <LeaderboardCard
                leaderboard={leaderboard}
                loading={leaderboardLoading}
                error={leaderboardError}
                onRefresh={refreshLeaderboard}
                cardVariants={cardVariants}
              />
            </div>
          </motion.div>

        </div>
      </motion.div>
    </MainLayout>
  )
}

export default ManagementDashboard
