import React from 'react'
import { cn } from '../../utils/cn'

interface MainLayoutProps {
  children: React.ReactNode
  title?: string
  description?: string
  className?: string
  topContent?: React.ReactNode
  bottomContent?: React.ReactNode
}

/**
 * Content layout component - only handles content area without sidebar
 * Used within the AppLayout that contains the persistent sidebar
 */
const MainLayout: React.FC<MainLayoutProps> = React.memo(({
  children,
  title,
  description,
  className,
  topContent,
  bottomContent
}) => {
  return (
    <main className={cn(
      "flex-1 flex flex-col h-screen overflow-hidden",
      "min-w-0", // Prevent flex item from overflowing
      className
    )}>
      {/* Header - No padding for seamless design */}
      {(title || description) && (
        <header className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex-shrink-0">
          <div className="flex h-12 sm:h-14 xl:h-16 items-center px-4 lg:px-6">
            {/* Title and description */}
            <div className="flex flex-col min-w-0 flex-1">
              {title && (
                <h1 className="text-sm sm:text-base lg:text-lg font-semibold text-foreground truncate">
                  {title}
                </h1>
              )}
              {description && (
                <p className="text-xs sm:text-sm text-muted-foreground truncate hidden sm:block">
                  {description}
                </p>
              )}
            </div>
          </div>
        </header>
      )}

      {/* Top Content (Filters) - No extra padding */}
      {topContent && (
        <div className="border-b border-border bg-background flex-shrink-0">
          <div className="px-4 lg:px-6 py-2">
            {topContent}
          </div>
        </div>
      )}

      {/* Content Area - No padding, full height */}
      <div className="flex-1 overflow-hidden">
        {children}
      </div>

      {/* Bottom Content (Pagination) - No extra padding */}
      {bottomContent && (
        <div className="border-t border-border bg-background flex-shrink-0">
          <div className="px-4 lg:px-6 py-2">
            {bottomContent}
          </div>
        </div>
      )}
    </main>
  )
})

MainLayout.displayName = 'MainLayout'

export default MainLayout
